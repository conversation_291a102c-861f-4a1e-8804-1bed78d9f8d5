<template>
  <v-form @submit.prevent>
    <v-container>
      <v-alert
        closable
        title="Opslaan gelukt"
        type="success"
        v-if="saveSuccess"
      ></v-alert>
      <v-alert
        closable
        title="Opslaan mislukt"
        :text="saveError"
        type="error"
        v-if="saveError"
      ></v-alert>
      <v-card-text class="text-caption">
        <PERSON><PERSON> hier welk audio apparaat de input moet leveren voor de transcriptie
      </v-card-text>
      <v-autocomplete
        label="Audio"
        :items="devices"
        v-model="device"
        :menu-props="{ eager: true }"
      ></v-autocomplete>
      <v-card-text class="text-caption">
        Minimaal volume in RMS van de microfoon voordat er spraak opgenomen
        wordt
      </v-card-text>
      <v-slider
        v-model="minMicVolume"
        :max="1000"
        :min="0"
        :step="1"
        class="align-center"
        hide-details
      >
        <template v-slot:append>
          <v-text-field
            v-model="minMicVolume"
            density="compact"
            style="width: 100px"
            type="number"
            hide-details
            single-line
          ></v-text-field>
        </template>
      </v-slider>
      <v-card-text class="text-caption">
        Minimaal volume in dB van het segment voordat het verwerkt wordt
      </v-card-text>
      <v-slider
        v-model="minSegVolumeDb"
        :max="200"
        :min="0"
        :step="1"
        class="align-center"
        hide-details
      >
        <template v-slot:append>
          <v-text-field
            v-model="minSegVolumeDb"
            density="compact"
            style="width: 100px"
            type="number"
            hide-details
            single-line
          ></v-text-field>
        </template>
      </v-slider>
      <v-card-text class="text-caption"> Taal van de preek </v-card-text>
      <v-autocomplete
        label="Talen"
        :items="languages"
        item-title="name"
        item-value="code"
        v-model="language"
        :menu-props="{ eager: true }"
      ></v-autocomplete>
      <v-card-text class="text-caption">
        Minimale en maximale tijd van een tekstfragment
      </v-card-text>
      <v-range-slider
        v-model="minAndMaxSeconds"
        strict
        :min="0"
        :max="30"
        :step="1"
      >
        <template v-slot:prepend>
          <v-text-field
            v-model="minAndMaxSeconds[0]"
            density="compact"
            style="width: 70px"
            type="number"
            variant="outlined"
            hide-details
            single-line
          ></v-text-field>
        </template>
        <template v-slot:append>
          <v-text-field
            v-model="minAndMaxSeconds[1]"
            density="compact"
            style="width: 70px"
            type="number"
            variant="outlined"
            hide-details
            single-line
          ></v-text-field> </template
      ></v-range-slider>
      <v-card-text class="text-caption">
        Behulpzame woorden die de tekst verbeteren.
      </v-card-text>
      <v-combobox
        label="Veel voorkomende woorden"
        v-model="commonWords"
        chips
        multiple
      ></v-combobox>
      <v-card-text class="text-caption">
        Wanneer deze woorden in een zin staan, wordt de hele zin verwijderd.
      </v-card-text>
      <v-combobox
        label="Genegeerde woorden"
        v-model="ignoredWords"
        chips
        multiple
      ></v-combobox>
      <div class="d-flex">
        <v-checkbox v-model="enableVad" label="VAD inschakelen"></v-checkbox>
        <v-checkbox
          v-model="saveFile"
          label="Audiobestand opslaan"
        ></v-checkbox>
        <v-checkbox
          v-model="saveTranslationCsvLog"
          label="Transcriptie als CSV opslaan"
        ></v-checkbox>
        <v-checkbox
          v-model="finalTranscriptionOnly"
          label="Alleen definitieve transcriptie"
          hint="Toon alleen de verbeterde, definitieve transcriptie (langzamer maar nauwkeuriger)"
        ></v-checkbox>
      </div>

      <!-- Automatic Recording Management Section -->
      <v-divider class="my-4"></v-divider>
      <v-card-text class="text-h6">
        Automatisch opname beheer
      </v-card-text>
      <v-card-text class="text-caption">
        Automatisch opnames uploaden naar Preeklezen na stilte en automatisch starten na spraakactiviteit
      </v-card-text>

      <div class="d-flex">
        <v-checkbox
          v-model="enableAutoRecording"
          label="Automatisch opname beheer inschakelen"
        ></v-checkbox>
      </div>

      <div v-if="enableAutoRecording">
        <v-card-text class="text-caption">
          Aantal minuten stilte voordat opname automatisch wordt geüpload naar Preeklezen
        </v-card-text>
        <v-slider
          v-model="autoRecordingSilenceTimeoutMinutes"
          :max="60"
          :min="1"
          :step="1"
          class="align-center"
          hide-details
        >
          <template v-slot:append>
            <v-text-field
              v-model="autoRecordingSilenceTimeoutMinutes"
              density="compact"
              style="width: 100px"
              type="number"
              hide-details
              single-line
              suffix="min"
            ></v-text-field>
          </template>
        </v-slider>

        <v-card-text class="text-caption">
          Aantal spraakberichten voordat automatisch opname start
        </v-card-text>
        <v-slider
          v-model="autoRecordingVoiceActivityThreshold"
          :max="10"
          :min="1"
          :step="1"
          class="align-center"
          hide-details
        >
          <template v-slot:append>
            <v-text-field
              v-model="autoRecordingVoiceActivityThreshold"
              density="compact"
              style="width: 100px"
              type="number"
              hide-details
              single-line
              suffix="berichten"
            ></v-text-field>
          </template>
        </v-slider>
      </div>
      <div class="d-flex">
        <v-btn @click="reset()"> Reset </v-btn>
        <v-spacer></v-spacer>
        <v-btn color="primary" type="submit" @click="send()"> Verzenden </v-btn>
      </div>
    </v-container>
  </v-form>
</template>

<script setup>
import languages from "@/assets/languages.json";

definePageMeta({
  title: "Instellingen",
  layout: "secure",
});

const settingsStore = useSettingsStore();

const device = ref(settingsStore.settings["alsaInput"]);
const language = ref(settingsStore.settings["language"]);
const minMicVolume = ref(settingsStore.settings["minMicVolume"]);
const minSegVolumeDb = ref(settingsStore.settings["minSegVolumeDb"]);
const commonWords = ref(settingsStore.settings["commonWords"]);
const ignoredWords = ref(settingsStore.settings["ignoredWords"]);
const minAndMaxSeconds = ref([
  settingsStore.settings["minStepS"],
  settingsStore.settings["maxStepS"],
]);
const enableVad = ref(settingsStore.settings["enableVad"]);
const saveFile = ref(settingsStore.settings["saveFile"]);
const saveTranslationCsvLog = ref(
  settingsStore.settings["saveTranslationCsvLog"]
);
const finalTranscriptionOnly = ref(
  settingsStore.settings["finalTranscriptionOnly"]
);
const enableAutoRecording = ref(
  settingsStore.settings["enableAutoRecording"] || false
);
const autoRecordingSilenceTimeoutMinutes = ref(
  settingsStore.settings["autoRecordingSilenceTimeoutMinutes"] || 15
);
const autoRecordingVoiceActivityThreshold = ref(
  settingsStore.settings["autoRecordingVoiceActivityThreshold"] || 2
);

const devices = await $fetch(`http://${location.hostname}:5001/devices`);

function reset() {
  device.value = settingsStore.settings["alsaInput"];
  language.value = settingsStore.settings["language"];
  minMicVolume.value = settingsStore.settings["minMicVolume"];
  minSegVolumeDb.value = settingsStore.settings["minSegVolumeDb"];
  commonWords.value = settingsStore.settings["commonWords"];
  ignoredWords.value = settingsStore.settings["ignoredWords"];
  minAndMaxSeconds.value = [
    settingsStore.settings["minStepS"],
    settingsStore.settings["maxStepS"],
  ];
  enableVad.value = settingsStore.settings["enableVad"];
  saveFile.value = settingsStore.settings["saveFile"];
  saveTranslationCsvLog.value = settingsStore.settings["saveTranslationCsvLog"];
  finalTranscriptionOnly.value =
    settingsStore.settings["finalTranscriptionOnly"];
  enableAutoRecording.value =
    settingsStore.settings["enableAutoRecording"] || false;
  autoRecordingSilenceTimeoutMinutes.value =
    settingsStore.settings["autoRecordingSilenceTimeoutMinutes"] || 15;
  autoRecordingVoiceActivityThreshold.value =
    settingsStore.settings["autoRecordingVoiceActivityThreshold"] || 2;
}

const saveSuccess = ref(false);
const saveError = ref(false);

async function send() {
  try {
    saveSuccess.value = false;
    saveError.value = false;
    settingsStore.settings = await $fetch(
      `http://${location.hostname}:5001/settings`,
      {
        method: "PATCH",
        body: {
          alsaInput: device.value,
          minMicVolume: minMicVolume.value,
          minSegVolumeDb: minSegVolumeDb.value,
          language: language.value,
          commonWords: commonWords.value,
          ignoredWords: ignoredWords.value,
          minStepS: minAndMaxSeconds.value[0],
          maxStepS: minAndMaxSeconds.value[1],
          enableVad: enableVad.value,
          saveFile: saveFile.value,
          saveTranslationCsvLog: saveTranslationCsvLog.value,
          finalTranscriptionOnly: finalTranscriptionOnly.value,
          enableAutoRecording: enableAutoRecording.value,
          autoRecordingSilenceTimeoutMinutes: autoRecordingSilenceTimeoutMinutes.value,
          autoRecordingVoiceActivityThreshold: autoRecordingVoiceActivityThreshold.value,
        },
      }
    );

    saveSuccess.value = true;
  } catch (e) {
    saveError.value = true;
  }
}
</script>
