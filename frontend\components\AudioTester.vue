<template>
  <v-card class="audio-tester" elevation="2">
    <v-card-title class="text-h6">Audio Test</v-card-title>
    <v-card-text>
      <div class="d-flex align-center mb-4">
        <v-btn
          :color="isTestingAudio ? 'error' : 'primary'"
          :disabled="!selectedDevice"
          @click="toggleAudioTest"
          class="mr-4"
        >
          {{ isTestingAudio ? 'Stop Test' : 'Start Test' }}
        </v-btn>
        <v-chip
          :color="isTestingAudio ? 'success' : 'default'"
          variant="outlined"
        >
          {{ isTestingAudio ? 'Testing' : 'Stopped' }}
        </v-chip>
      </div>

      <!-- Audio Level Meter -->
      <div class="audio-meter mb-4">
        <div class="meter-label d-flex justify-space-between mb-2">
          <span class="text-caption">Audio Level</span>
          <span class="text-caption font-weight-bold">
            {{ currentDb.toFixed(1) }} dB
          </span>
        </div>
        
        <!-- Visual meter bar -->
        <div class="meter-container">
          <div class="meter-background">
            <div 
              class="meter-fill"
              :style="{ width: meterPercentage + '%' }"
              :class="getMeterColorClass()"
            ></div>
          </div>
          
          <!-- dB scale markers -->
          <div class="meter-scale">
            <div class="scale-marker" style="left: 0%">-60</div>
            <div class="scale-marker" style="left: 33%">-40</div>
            <div class="scale-marker" style="left: 66%">-20</div>
            <div class="scale-marker" style="left: 100%">0</div>
          </div>
        </div>
      </div>

      <!-- RMS Value Display -->
      <div class="d-flex justify-space-between">
        <span class="text-caption">RMS Value:</span>
        <span class="text-caption font-weight-bold">{{ currentRms.toFixed(2) }}</span>
      </div>

      <!-- Connection Status -->
      <v-alert
        v-if="connectionError"
        type="error"
        variant="tonal"
        class="mt-4"
        :text="connectionError"
      ></v-alert>
    </v-card-text>
  </v-card>
</template>

<script setup>
const props = defineProps({
  selectedDevice: {
    type: String,
    default: ''
  }
})

const isTestingAudio = ref(false)
const currentDb = ref(-60)
const currentRms = ref(0)
const connectionError = ref('')
let eventSource = null

// Computed property for meter percentage (0-100%)
const meterPercentage = computed(() => {
  // Convert dB range (-60 to 0) to percentage (0 to 100)
  const normalizedDb = Math.max(0, Math.min(60, currentDb.value + 60))
  return (normalizedDb / 60) * 100
})

// Get color class based on dB level
const getMeterColorClass = () => {
  if (currentDb.value > -10) return 'meter-red'
  if (currentDb.value > -20) return 'meter-yellow'
  return 'meter-green'
}

const toggleAudioTest = () => {
  if (isTestingAudio.value) {
    stopAudioTest()
  } else {
    startAudioTest()
  }
}

const startAudioTest = () => {
  if (!props.selectedDevice) {
    connectionError.value = 'Please select an audio device first'
    return
  }

  connectionError.value = ''
  
  // Create EventSource connection to the audio test endpoint
  const url = `http://${location.hostname}:5001/audio-test?device=${encodeURIComponent(props.selectedDevice)}`
  eventSource = new EventSource(url)
  
  eventSource.onopen = () => {
    isTestingAudio.value = true
    connectionError.value = ''
  }
  
  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      currentRms.value = data.rms || 0
      currentDb.value = data.db || -60
    } catch (error) {
      console.error('Error parsing audio data:', error)
    }
  }
  
  eventSource.onerror = (error) => {
    console.error('EventSource error:', error)
    connectionError.value = 'Failed to connect to audio test endpoint'
    stopAudioTest()
  }
}

const stopAudioTest = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
  isTestingAudio.value = false
  currentDb.value = -60
  currentRms.value = 0
}

// Clean up on component unmount
onUnmounted(() => {
  stopAudioTest()
})

// Watch for device changes and restart test if needed
watch(() => props.selectedDevice, (newDevice, oldDevice) => {
  if (isTestingAudio.value && newDevice !== oldDevice) {
    stopAudioTest()
    if (newDevice) {
      nextTick(() => {
        startAudioTest()
      })
    }
  }
})
</script>

<style scoped>
.audio-tester {
  max-width: 100%;
}

.audio-meter {
  width: 100%;
}

.meter-container {
  position: relative;
  width: 100%;
}

.meter-background {
  width: 100%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.meter-fill {
  height: 100%;
  transition: width 0.1s ease-out;
  border-radius: 10px;
}

.meter-green {
  background: linear-gradient(90deg, #4caf50, #8bc34a);
}

.meter-yellow {
  background: linear-gradient(90deg, #8bc34a, #ffeb3b, #ff9800);
}

.meter-red {
  background: linear-gradient(90deg, #ff9800, #f44336);
}

.meter-scale {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-top: 4px;
  width: 100%;
}

.scale-marker {
  position: absolute;
  font-size: 10px;
  color: #666;
  transform: translateX(-50%);
}
</style>
